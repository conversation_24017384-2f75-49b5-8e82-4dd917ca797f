import React, { useEffect, useId, useState } from "react";
import { EmblaOptionsType } from "embla-carousel";
import Carousel, { <PERSON><PERSON><PERSON>, Slider<PERSON>ontainer, ThumsSlider } from "../ui/carousel";
import Image from "next/image";
import { AnimatePresence, motion, MotionConfig } from "framer-motion";
import { XIcon } from "lucide-react";
import { getImageUrl } from "@/utils/imageUtils";

const transition = {
  type: "spring",
  duration: 0.4,
};

interface ImageCarouselProps {
  productImage: any[];
  brand?: {
    id?: number;
    name: string;
    image_url?: string;
    image?: string;
  } | string;
}

export default function ImageCarousel({ productImage, brand }: ImageCarouselProps) {
  const OPTIONS: EmblaOptionsType = { loop: true };
  const [isMediaModalOpen, setIsMediaModalOpen] = useState<string | undefined>(undefined);
  const uniqueId = useId();

  useEffect(() => {
     if (typeof window === 'undefined') return;
    if (isMediaModalOpen) {
      // Prevent body scroll and maintain scroll position
         window.scrollTo(0, 0);

    // Add image modal class to body to trigger CSS rules
    document.body.classList.add('image-modal-open');

    // Then prevent body scroll with fixed position
    document.body.style.position = 'fixed';
    document.body.style.top = '0px';
    document.body.style.width = '100%';
    document.body.style.overflow = 'hidden';
    } else {
      // Remove image modal class from body
      document.body.classList.remove('image-modal-open');

      // Restore body scroll and position
      const scrollY = document.body.style.top;
      document.body.style.position = '';
      document.body.style.top = '';
      document.body.style.width = '';
      document.body.style.overflow = '';
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0') * -1);
      }
    }

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsMediaModalOpen(undefined);
      }
    };

    document.addEventListener("keydown", handleKeyDown);
    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      // Cleanup on unmount
      if (isMediaModalOpen) {
        // Remove image modal class from body
        document.body.classList.remove('image-modal-open');

        const scrollY = document.body.style.top;
        document.body.style.position = '';
        document.body.style.top = '';
        document.body.style.width = '';
        document.body.style.overflow = '';
        if (scrollY) {
          window.scrollTo(0, parseInt(scrollY || '0') * -1);
        }
      }
    };
  }, [isMediaModalOpen]);

  return (
    <MotionConfig transition={transition}>
      <div className="w-full bg-background mx-auto relative">
        {/* Brand Badge - Image only positioned at the top right of the carousel for better visibility */}
        {brand && typeof brand !== 'string' && (brand?.image_url || brand?.image) && (
          <div className="absolute top-3 right-3 z-20">
            <div className="w-12 h-12 sm:w-14 sm:h-14 overflow-hidden rounded-lg border-2 border-white shadow-lg bg-white flex items-center justify-center p-1">
              <img
                src={brand?.image_url || `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${brand?.image}`}
                alt={`${brand?.name} logo`}
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  // Hide the image on error
                  const imgElement = e.currentTarget as HTMLImageElement;
                  imgElement.style.display = 'none';
                }}
              />
            </div>
          </div>
        )}
        <Carousel options={OPTIONS} className="relative" isAutoPlay={true}>
          <SliderContainer className="gap-2">
            {productImage.map((image: any) => (
              <Slider
                key={image.id}
                className="xl:h-[500px] lg:h-[450px] md:h-[400px] sm:h-[350px] h-[300px] w-full"
                thumnailSrc={getImageUrl(image?.image)}
              >
                <div className="w-full h-full flex items-center justify-center p-2">
                  <Image
                    onClick={() => {
                      setIsMediaModalOpen(getImageUrl(image?.image));
                    }}
                    src={getImageUrl(image?.image)}
                    width={1400}
                    height={800}
                    alt="image"
                    className="max-h-full max-w-full object-contain rounded-lg cursor-pointer transition-transform duration-200 hover:scale-105"
                    style={{ width: 'auto', height: 'auto' }}
                  />
                </div>
              </Slider>
            ))}
          </SliderContainer>
          <ThumsSlider />
        </Carousel>

        <AnimatePresence initial={false} mode="sync">
          {isMediaModalOpen && (
            <>
              <motion.div
                key={`backdrop-${uniqueId}`}
                className="image-modal-backdrop fixed inset-0 h-full w-full backdrop-blur-sm bg-black/80 z-[9998]"
                variants={{ open: { opacity: 1 }, closed: { opacity: 0 } }}
                initial="closed"
                animate="open"
                exit="closed"
                onClick={() => {
                  setIsMediaModalOpen(undefined);
                }}
              />
              <motion.div
                key="dialog"
                className="pointer-events-none fixed inset-0 flex items-center justify-center z-[9999]"
              >
                <motion.div
                  className="image-modal-content pointer-events-auto relative flex flex-col overflow-hidden dark:bg-gray-950 bg-gray-200 border w-[90%] h-[90%] max-w-5xl shadow-2xl"
                  layoutId={`dialog-${uniqueId}`}
                  tabIndex={-1}
                  data-image-modal={uniqueId}
                  role="dialog"
                  style={{
                    borderRadius: "24px",
                  }}
                >
                  {/* Close button */}
                  {/* <button
                    onClick={() => setIsMediaModalOpen(undefined)}
                    className="absolute top-4 right-4 z-10 p-2 rounded-full bg-black/50 text-white hover:bg-black/70 transition-colors"
                    aria-label="Close modal"
                  >
                    <XIcon className="h-6 w-6" />
                  </button> */}

                  {isMediaModalOpen && (
                    <motion.div
                      layoutId={`dialog-img-${uniqueId}`}
                      className="w-full h-full flex items-center justify-center p-4"
                    >
                      {/* eslint-disable-next-line @next/next/no-img-element */}
                      <img
                        src={isMediaModalOpen}
                        alt=""
                        className="max-h-full max-w-full object-contain"
                        style={{ width: 'auto', height: 'auto' }}
                      />
                    </motion.div>
                  )}

                  <button
                    onClick={() => setIsMediaModalOpen(undefined)}
                    className="absolute right-6 top-6 p-3 text-zinc-50 cursor-pointer dark:bg-gray-900 bg-gray-400 hover:bg-gray-500 rounded-full dark:hover:bg-gray-800"
                    type="button"
                    aria-label="Close dialog"
                  >
                    <XIcon size={24} />
                  </button>
                </motion.div>
              </motion.div>
            </>
          )}
        </AnimatePresence>
      </div>
    </MotionConfig>
  );
}